package com.dateup.android.subscriptions.viewModels

import android.annotation.SuppressLint
import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.Purchase
import com.dateup.android.DateUpApplication
import com.dateup.android.ServiceResponse
import com.dateup.android.subscriptions.SubscriptionConstants.HALF_YEARLY_SKU
import com.dateup.android.subscriptions.SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER
import com.dateup.android.subscriptions.SubscriptionConstants.MONTHLY_SKU
import com.dateup.android.subscriptions.SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER
import com.dateup.android.subscriptions.SubscriptionConstants.SELECT_HALF_YEARLY_SKU
import com.dateup.android.subscriptions.SubscriptionConstants.SELECT_MONTHLY_SKU
import com.dateup.android.subscriptions.SubscriptionConstants.SELECT_THREE_MONTHS_SKU
import com.dateup.android.subscriptions.SubscriptionConstants.THREE_MONTHS_SKU
import com.dateup.android.subscriptions.billing.*
import com.dateup.android.subscriptions.model.PurchaseAcknowledgmentStatus
import com.dateup.android.subscriptions.model.SubscriptionStatus
import com.dateup.android.subscriptions.views.SingleLiveEvent
import kotlinx.coroutines.launch
import timber.log.Timber

@SuppressLint("BinaryOperationInTimber")
class BillingViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        
        const val TAG = "BillingViewModel"
    }

    private val repository = (application as DateUpApplication).repository
    val subscriptions: LiveData<List<SubscriptionStatus>> = repository.subscriptions

    /**
     * Local billing purchase data.
     */
    private val purchases = (application as DateUpApplication).billingClientLifecycle.purchases

    private val billingClientLifecycle = (application as DateUpApplication).billingClientLifecycle

    /**
     * ProductDetails for all known products.
     */
    private val productsWithProductDetails = (application as DateUpApplication).billingClientLifecycle.productsWithProductDetails

    /**
     * Send an event when the Activity needs to buy something.
     */
    val buyEvent = SingleLiveEvent<BillingFlowParams>()

    /**
     * Send an event when the UI should open the Google Play
     * Store for the user to manage their subscriptions.
     */
    val openPlayStoreSubscriptionsEvent = SingleLiveEvent<String>()

    /**
     * Buy a premium subscription.
     */
    fun buySubscription(subscriptionId: String) {
        val oldSubscriptionId = checkTheSubscriptionUserAlreadyHas(purchases.value)
        val alreadyHasNewSubscription = deviceHasGooglePlaySubscription(purchases.value, subscriptionId)
        if (alreadyHasNewSubscription) {
            Timber.e("User already has this subscription. They are trying to buy the same again")
        } else {
            buy(subscriptionId, oldSubscriptionId)
        }
    }

    fun userAlreadyHasTrailSubscription(): Boolean {
        return deviceHasGooglePlaySubscription(purchases.value, MONTHLY_SKU_WITH_TRAIL_OFFER)
    }

    fun userAlreadyHasInfluencerTrailSubscription(): Boolean {
        return deviceHasGooglePlaySubscription(purchases.value, INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER)
    }
    
    fun userHasSelectSubscription(): Boolean {
        return subscriptions.value?.any { 
            it.sku in listOf(
                SELECT_MONTHLY_SKU,
                SELECT_THREE_MONTHS_SKU,
                SELECT_HALF_YEARLY_SKU,
            )
        } ?: false
    }

    /**
     * Use the Google Play Billing Library to make a purchase.
     */
    private fun buy(productId: String, oldProductId: String?) {
        // First, determine whether the new product can be purchased.
        val isProductOnServer = serverHasSubscription(subscriptions.value, productId)
        val isProductOnDevice = deviceHasGooglePlaySubscription(purchases.value, productId)
        Timber.tag(TAG).d("$productId - isProductOnServer: $isProductOnServer, isProductOnDevice: $isProductOnDevice")
        when {
            isProductOnDevice && isProductOnServer -> {
                Timber.tag(TAG).e("You cannot buy a product that is already owned: $productId. " +
                        "This is an error in the application trying to use Google Play Billing.")
                return
            }
            isProductOnDevice && !isProductOnServer -> {
                Timber.tag(TAG).e("The Google Play Billing Library APIs indicate that " +
                        "this product is already owned, but the purchase token is not registered " +
                        "with the server. There might be an issue registering the purchase token.")
                return
            }
            !isProductOnDevice && isProductOnServer -> {
                Timber.tag(TAG).e("The server says that the user already owns " +
                        "this item: $productId. This could be from another Google account. " +
                        "You should warn the user that they are trying to buy something " +
                        "from Google Play that they might already have access to from " +
                        "another purchase, possibly from a different Google account " +
                        "on another device.\n" +
                        "You can choose to block this purchase.\n" +
                        "If you are able to cancel the existing subscription on the server, " +
                        "you should allow the user to subscribe with Google Play, and then " +
                        "cancel the subscription after this new subscription is complete. " +
                        "This will allow the user to seamlessly transition their payment " +
                        "method from an existing payment method to this Google Play account.")
                return
            }
        }

        // Second, determine whether the old product can be replaced.
        // If the old product cannot be used, set this value to null and ignore it.
        val oldProductToBeReplaced = if (isOldProductReplaceable(subscriptions.value, purchases.value, oldProductId)) {
            oldProductId
        } else {
            null
        }

        // Create the parameters for the purchase.
        val productDetails = productsWithProductDetails.value?.get(productId) ?: run {
            Timber.tag(TAG).e("Could not find ProductDetails to make purchase.")
            return
        }
        
        // Build purchase params with ProductDetails
        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .apply {
                    // For subscriptions, get the base plan or offer token
                    productDetails.subscriptionOfferDetails?.firstOrNull()?.let { offerDetails ->
                        setOfferToken(offerDetails.offerToken)
                    }
                }
                .build()
        )

        val billingBuilder = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(productDetailsParamsList)
            
        // Only set the old product parameter if the old product is already owned.
        if (oldProductToBeReplaced != null && oldProductToBeReplaced != productId) {
            purchaseForSku(purchases.value, oldProductToBeReplaced)?.apply {
                val isUpgradeToSelect = isSelectSubscription(productId) && isPlusSubscription(oldProductToBeReplaced)
                
                val updateParamsBuilder = BillingFlowParams.SubscriptionUpdateParams.newBuilder()
                        .setOldPurchaseToken(purchaseToken)
                
                if (isUpgradeToSelect) {
                    updateParamsBuilder.setSubscriptionReplacementMode(
                        BillingFlowParams.SubscriptionUpdateParams.ReplacementMode.CHARGE_PRORATED_PRICE
                    )
                }
                
                billingBuilder.setSubscriptionUpdateParams(updateParamsBuilder.build())
            }
        }
        val billingParams = billingBuilder.build()

        // Send the parameters to the Activity in order to launch the billing flow.
        buyEvent.postValue(billingParams)
    }

    /**
     * Determine if the old product can be replaced.
     */
    private fun isOldProductReplaceable(
            subscriptions: List<SubscriptionStatus>?,
            purchases: List<Purchase>?,
            oldProductId: String?
    ): Boolean {
        if (oldProductId == null) return false
        val isOldProductOnServer = serverHasSubscription(subscriptions, oldProductId)
        val isOldProductOnDevice = deviceHasGooglePlaySubscription(purchases, oldProductId)
        return when {
            !isOldProductOnDevice -> {
                Timber.tag(TAG).e("You cannot replace a product that is NOT already owned: $oldProductId. " +
                        "This is an error in the application trying to use Google Play Billing.")
                false
            }
            !isOldProductOnServer -> {
                Timber.tag(TAG).e("Refusing to replace the old product because it is " +
                        "not registered with the server. Instead just buy the new product as an " +
                        "original purchase. The old product might already " +
                        "be owned by a different app account, and we should not transfer the " +
                        "subscription without user permission.")
                false
            }
            else -> {
                val subscription = subscriptionForSku(subscriptions, oldProductId) ?: return false
                if (subscription.subAlreadyOwned) {
                    Timber.tag(TAG).i("The old subscription is used by a " +
                            "different app account. However, it was paid for by the same " +
                            "Google account that is on this device.")
                    false
                } else {
                    true
                }
            }
        }
    }

    fun registerSubscription(sku: String, purchaseToken: String, callback: (ServiceResponse) -> Unit) {
        viewModelScope.launch {
            repository.registerSubscription(sku, purchaseToken, callback)
        }
    }

    fun saveAcknowledgment(purchaseAcknowledgmentStatusList: List<PurchaseAcknowledgmentStatus>) {
        viewModelScope.launch {
            repository.saveAcknowledgment(purchaseAcknowledgmentStatusList)
        }
    }
    
    /**
     * Helper method to check if a subscription SKU is a Select subscription
     */
    private fun isSelectSubscription(sku: String): Boolean {
        return sku in listOf(
            SELECT_MONTHLY_SKU,
            SELECT_THREE_MONTHS_SKU,
            SELECT_HALF_YEARLY_SKU,
        )
    }

    /**
     * Helper method to check if a subscription SKU is a Plus subscription
     */
    private fun isPlusSubscription(sku: String): Boolean {
        return sku in listOf(
            MONTHLY_SKU,
            THREE_MONTHS_SKU,
            HALF_YEARLY_SKU,
            MONTHLY_SKU_WITH_TRAIL_OFFER,
            INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER
        )
    }

    fun checkPurchasesAndAcknowledge(purchaseList: List<Purchase>) {
        viewModelScope.launch {
            repository.checkForSubscriptionStatusAndUpdateDB(purchaseList)
            repository.checkForAcknowledgementAndUpdateDB(purchaseList) { nonAckList ->
                billingClientLifecycle.acknowledgePurchases(nonAckList) { statusList ->
                    saveAcknowledgment(statusList)
                }
            }
        }
    }
}
