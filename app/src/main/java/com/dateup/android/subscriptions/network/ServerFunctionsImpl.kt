/*
 * Copyright 2018 Google LLC. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dateup.android.subscriptions.network

import androidx.lifecycle.MutableLiveData
import com.dateup.android.ServiceResponse
import com.dateup.android.models.UserObject
import com.dateup.android.subscriptions.SubscriptionConstants.HALF_YEARLY_SKU
import com.dateup.android.subscriptions.SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER
import com.dateup.android.subscriptions.SubscriptionConstants.MONTHLY_SKU
import com.dateup.android.subscriptions.SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER
import com.dateup.android.subscriptions.SubscriptionConstants.SELECT_PRODUCT_ID
import com.dateup.android.subscriptions.SubscriptionConstants.THREE_MONTHS_SKU
import com.dateup.android.subscriptions.model.SubscriptionStatus
import com.dateup.android.utils.Constants
import com.google.firebase.functions.FirebaseFunctions
import com.google.firebase.functions.FirebaseFunctionsException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber

class ServerFunctionsImpl {

    /**
     * The latest subscription data from the Firebase server.
     *
     * Use this class by observing the subscriptions LiveData.
     * Any server updates will be communicated through this LiveData.
     */
    val subscriptions = MutableLiveData<List<SubscriptionStatus>>()

    /**
     * Expected errors.
     *
     * NOT_FOUND: Invalid SKU or purchase token.
     * ALREADY_OWNED: Subscription is claimed by a different user.
     * INTERNAL: Server error.
     */
    enum class ServerError {
        NOT_FOUND, ALREADY_OWNED, PERMISSION_DENIED, INTERNAL
    }

    /**
     * Singleton instance of the Firebase Functions API.
     */
    private val firebaseFunctions = FirebaseFunctions.getInstance()

    /**
     * Register a subscription with the server and posts successful results to [subscriptions].
     * This is the function that validates the purchase against google play.
     */
    suspend fun registerSubscription(sku: String, purchaseToken: String, callback: (ServiceResponse) -> Unit) {
        withContext(Dispatchers.IO) {
            when (sku) {
                // Plus subscriptions
                MONTHLY_SKU,
                THREE_MONTHS_SKU,
                HALF_YEARLY_SKU,
                MONTHLY_SKU_WITH_TRAIL_OFFER,
                INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER -> {
                    UserObject.isDateUpPlusUser = true
                    handleSubscriptionRegistration(sku, purchaseToken, callback)
                }
                // Select subscriptions
                SELECT_PRODUCT_ID -> {
                    UserObject.isDateUpSelectUser = true
                    handleSubscriptionRegistration(sku, purchaseToken, callback)
                }
                else -> {
                    // Default to Plus subscription for backward compatibility
                    UserObject.isDateUpPlusUser = true
                    handleSubscriptionRegistration(sku, purchaseToken, callback)
                }
            }
        }
    }

    /**
     * Handles the actual subscription registration with Firebase
     */
    private fun handleSubscriptionRegistration(sku: String, purchaseToken: String, callback: (ServiceResponse) -> Unit) {
        Timber.tag(TAG).d("Calling: $REGISTER_SUBSCRIPTION_CALLABLE")
        val data = HashMap<String, String>().apply {
            put(SKU_KEY, sku)
            put(PURCHASE_TOKEN_KEY, purchaseToken)
        }
        firebaseFunctions
                .getHttpsCallable(REGISTER_SUBSCRIPTION_CALLABLE)
                .call(data)
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        val result = (task.result?.getData() as? Map<String, Any>)?.let {
                            SubscriptionStatus.listFromMap(it)
                        }
                        Timber.tag(TAG).i("Subscription registration successful: $result")
                        if (result == null) {
                            Timber.tag(TAG).e("Invalid subscription registration data")
                        } else {
                            subscriptions.postValue(result)
                        }
                        callback(ServiceResponse.getServiceResponseOnSuccess())
                    } else {
                        callback(ServiceResponse.getServiceResponseOnError())
                        when (serverErrorFromFirebaseException(task.exception)) {
                            ServerError.NOT_FOUND -> {
                                Timber.tag(TAG).e("Invalid SKU or purchase token during registration: ${task.exception}")
                            }
                            ServerError.ALREADY_OWNED -> {
                                Timber.tag(TAG).i("Subscription already owned by another user: ${task.exception}")
                            }
                            ServerError.INTERNAL -> {
                                Timber.tag(TAG).e("Subscription registration server error: ${task.exception}")
                            }
                            else -> {
                                Timber.tag(TAG).e("Unknown error during subscription registration: ${task.exception}")
                            }
                        }
                    }
                }
    }

    /**
     * Convert Firebase error codes to the app-specific meaning.
     */
    private fun serverErrorFromFirebaseException(exception: Exception?): ServerError? {
        if (exception !is FirebaseFunctionsException) {
            Timber.tag(TAG).d("Unrecognized Exception: $exception")
            return null
        }
        val code = exception.code
        return when (exception.code) {
            FirebaseFunctionsException.Code.NOT_FOUND -> ServerError.NOT_FOUND
            FirebaseFunctionsException.Code.ALREADY_EXISTS -> ServerError.ALREADY_OWNED
            FirebaseFunctionsException.Code.PERMISSION_DENIED -> ServerError.PERMISSION_DENIED
            FirebaseFunctionsException.Code.INTERNAL -> ServerError.INTERNAL
            FirebaseFunctionsException.Code.RESOURCE_EXHAUSTED -> {
                Timber.tag(TAG).e("RESOURCE_EXHAUSTED: Check your server quota")
                ServerError.INTERNAL
            }
            else -> {
                Timber.tag(TAG).d("Unexpected Firebase Exception: $code")
                null
            }
        }
    }

    companion object {
        private const val TAG = "ServerImpl"

        // Keys for sending the cloud function in order to validate purchase
        private const val SKU_KEY = "sku"
        private const val PURCHASE_TOKEN_KEY = "token"

        // firebase cloud functions
        private const val REGISTER_SUBSCRIPTION_CALLABLE = "subscriptionRegistration"
    }

}
