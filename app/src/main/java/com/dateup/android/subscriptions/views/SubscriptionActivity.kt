package com.dateup.android.subscriptions.views

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.android.billingclient.api.Purchase
import com.dateup.android.AccountPreferences
import com.dateup.android.DateUpApplication
import com.dateup.android.R
import com.dateup.android.ScreenRouter
import com.dateup.android.ServiceResponse
import com.dateup.android.bottomSheet.DateUpBottomSheet
import com.dateup.android.databinding.ActivitySubscriptionV2Binding
import com.dateup.android.extensions.launchModeWithNoBackStack
import com.dateup.android.firebase.FirebaseDatabaseUtil
import com.dateup.android.models.SubscriptionsListItem
import com.dateup.android.subscriptions.SubscriptionConstants
import com.dateup.android.subscriptions.adapter.SubscriptionPanelsAdapter
import com.dateup.android.subscriptions.billing.BillingClientLifecycle
import com.dateup.android.subscriptions.billing.isSubscriptionRestore
import com.dateup.android.subscriptions.viewModels.BillingViewModel
import com.dateup.android.ui.browseProfiles.BrowseProfilesActivity
import com.dateup.android.utils.AlertDialogView
import timber.log.Timber

class SubscriptionActivity : AppCompatActivity() {

    private lateinit var billingClientLifecycle: BillingClientLifecycle
    private lateinit var billingViewModel: BillingViewModel

    //private lateinit var subscriptionsRecyclerAdapter: SubscriptionsListAdapter

    private lateinit var binding: ActivitySubscriptionV2Binding

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)

        binding = ActivitySubscriptionV2Binding.inflate(layoutInflater)
        this.setContentView(binding.root)

        binding.subscriptionViewPager.adapter = SubscriptionPanelsAdapter(layoutInflater)
        binding.dotsIndicator.setViewPager2(binding.subscriptionViewPager)

        billingViewModel = ViewModelProvider(this).get(BillingViewModel::class.java)

        billingClientLifecycle = (application as DateUpApplication).billingClientLifecycle

        getAndSetSubscriptionsList()

        binding.buttonJoinDateupPlus.setOnClickListener {
            showChooserDialog()
        }

        binding.closeButton.setOnClickListener {
            startBrowseProfilesScreen()
        }

        binding.buttonJoinDateupSelect.setOnClickListener {
            showSelectChooserDialog()
        }

        checkForSubscriptionRestore()

        val freeTrailUsed = AccountPreferences.getInstance(applicationContext).getBooleanValue(com.dateup.android.utils.Constants.freeTrailUsed, false)
        val influencerId = AccountPreferences.getInstance(applicationContext).getStringValue(com.dateup.android.utils.Constants.influencerId, "")

        val offerTrail = intent.getBooleanExtra(FREE_TRAIL, false)
        val influencerOfferTrail = intent.getBooleanExtra(INFLUENCER_FREE_TRAIL, false)
        val influencerName = AccountPreferences.getInstance(applicationContext).getStringValue(com.dateup.android.utils.Constants.influencerName, "")
        val influencerPromoShown = AccountPreferences.getInstance(applicationContext).getBooleanValue(com.dateup.android.utils.Constants.influencerPromoShown, false)

        if (!freeTrailUsed && !influencerPromoShown) {
            val counter:Int = AccountPreferences.getInstance(this@SubscriptionActivity).getIntValue(com.dateup.android.utils.Constants.promoCounter, 0)
            if (influencerOfferTrail
                && counter <=1
                && !billingViewModel.userAlreadyHasInfluencerTrailSubscription()
                && !influencerId.isNullOrEmpty()) {
                showInfluencerOfferTrail(influencerName, counter)
                if (counter>=1) {
                    AccountPreferences.getInstance(this@SubscriptionActivity).setValue(com.dateup.android.utils.Constants.influencerPromoShown, true)
                }
            } else if (!billingViewModel.userAlreadyHasTrailSubscription()
                && counter <=1
                && offerTrail) {
                showOfferTrail(counter)
                if (counter>=1) {
                    AccountPreferences.getInstance(this@SubscriptionActivity).setValue(com.dateup.android.utils.Constants.influencerPromoShown, true)
                }
            } else {
                hideTrailOffer()
            }
        } else {
            hideTrailOffer()
        }

        // Register purchases when they change.
        billingClientLifecycle.purchaseUpdateEvent.observe(this) {
            it?.let { purchaseList ->
                registerPurchases(it) { serviceResponse ->
                    if (serviceResponse.isSuccess()) {
                        billingClientLifecycle.acknowledgePurchases(purchaseList) { purchaseAcknowledgementStatusList ->
                            billingViewModel.saveAcknowledgment(purchaseAcknowledgementStatusList)
                        }
                    }
                }

                if (purchaseList.isNotEmpty()) {
                    val purchase = purchaseList[0]
                    when (purchase.purchaseState) {
                        Purchase.PurchaseState.PURCHASED -> {
                            startDateupCelebrationActivity()

                            val firebaseDatabaseReference = FirebaseDatabaseUtil(this)

                            // save the ad campaign source to firebase
                            val installSource =
                                AccountPreferences.getInstance(this@SubscriptionActivity)
                                    .getStringValue(
                                        com.dateup.android.utils.Constants.installSource,
                                        ""
                                    )
                            val phone =
                                "+1" + AccountPreferences.getInstance(this@SubscriptionActivity)
                                    .getStringValue(
                                        com.dateup.android.utils.Constants.phoneNumber, ""
                                    ).toString()

                            if (!installSource.isNullOrEmpty()) {
                                firebaseDatabaseReference.saveBranchAdCampaignSignupsInFirebase(
                                    installSource,
                                    phone
                                )
                            }

                            if (!freeTrailUsed && !influencerId.isNullOrEmpty()
                                && purchase.skus.isNotEmpty() && purchase.skus[0] == SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER
                            ) {
                                firebaseDatabaseReference.saveInfluencerSignupInFirebaseAndResetInPrefs(
                                    true,
                                    this@SubscriptionActivity
                                )
                            } else if (!freeTrailUsed && purchase.skus.isNotEmpty() && purchase.skus[0] == SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER) {
                                firebaseDatabaseReference.saveUserInfoToFirebase(
                                    com.dateup.android.utils.Constants.freeTrailUsed,
                                    true
                                )
                                AccountPreferences.getInstance(this@SubscriptionActivity).setValue(
                                    com.dateup.android.utils.Constants.freeTrailUsed,
                                    true
                                )
                            }
                        }

                        Purchase.PurchaseState.PENDING -> {
                            AlertDialogView.showAlertDialog(
                                context = this,
                                message = getString(R.string.purchase_pending),
                                buttonPositiveText = getString(R.string.okay),
                                buttonNegativeText = null
                            ) { _, which ->
                                if (which == DialogInterface.BUTTON_POSITIVE) {
                                    startBrowseProfilesScreen()
                                }
                            }
                        }

                        else -> {
                            AlertDialogView.showAlertDialog(
                                context = this,
                                message = getString(R.string.purchase_unspecified),
                                buttonPositiveText = getString(R.string.okay),
                                buttonNegativeText = null
                            ) { _, which ->
                                if (which == DialogInterface.BUTTON_POSITIVE) {
                                    startBrowseProfilesScreen()
                                }
                            }
                        }
                    }
                } else {
                    AlertDialogView.showAlertDialog(
                        context = this,
                        message = getString(R.string.purchase_unspecified),
                        buttonPositiveText = getString(R.string.okay),
                        buttonNegativeText = null
                    ) { _, which ->
                        if (which == DialogInterface.BUTTON_POSITIVE) {
                            startBrowseProfilesScreen()
                        }
                    }
                }
            }
        }

        // Launch the billing flow when the user clicks a button to buy something.
        billingViewModel.buyEvent.observe(this, {
            it?.let {
                billingClientLifecycle.launchBillingFlow(this, it)
            }
        })
        setupTitle()

        ScreenRouter.saveScreenInfoToFirebase(this, this::class.java.simpleName)
    }

    private fun setupTitle() {
        val subOriginIntent = intent.getStringExtra(SUBSCRIPTION_ORIGIN)
        if (subOriginIntent == OUT_OF_LIKES) {
            val spannableString: Spannable = SpannableString(getString(R.string.out_of_likes_subscription))
            spannableString.setSpan(ForegroundColorSpan(getColor(R.color.color_primary)), 45, 56, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.subscriptionTitleHeader.text = spannableString
        }
    }

    private fun startDateupCelebrationActivity() {
        val intent = DateUpPlusCelebrationActivity.newIntent(this).launchModeWithNoBackStack()
        startActivity(intent)
    }

    private fun startBrowseProfilesScreen() {
        val intent = BrowseProfilesActivity.newIntent(this).launchModeWithNoBackStack()
        this.startActivity(intent)
    }

    /**
     * Register SKUs and purchase tokens with the server.
     */
    private fun registerPurchases(
        purchaseList: List<Purchase>,
        callback: (ServiceResponse) -> Unit
    ) {
        for (purchase in purchaseList) {
            val sku = purchase.skus[0]
            val purchaseToken = purchase.purchaseToken
            billingViewModel.registerSubscription(
                sku = sku,
                purchaseToken = purchaseToken,
                callback
            )
        }
    }

    private fun getAndSetSubscriptionsList() {
        val list = listOf(
            SubscriptionsListItem(
                getDrawable(R.drawable.ic_woman_thinking),
                getString(R.string.subscription_all_likes),
                getString(R.string.subscription_all_likes_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.teleport_header),
                getString(R.string.subscription_teleport),
                getString(R.string.subscription_teleport_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.ic_likes),
                getString(R.string.subscription_unlimited_likes),
                getString(R.string.subscription_unlimited_likes_desc)
            ),
            SubscriptionsListItem(
                getDrawable(R.drawable.ic_read_receipts),
                getString(R.string.subscription_read_receipts),
                getString(R.string.subscription_read_receipts_desc)
            )
        )
//        subscriptionsRecyclerAdapter = SubscriptionsListAdapter(list)
//        binding.subscriptionBenefitsList.adapter = subscriptionsRecyclerAdapter
    }

    private fun checkForSubscriptionRestore() {
        if (shouldShowSubscriptionRestoreAction().first) {
            binding.restoreTextView.visibility = View.VISIBLE
            binding.restoreTextView.setOnClickListener {
                manageSubscriptions(shouldShowSubscriptionRestoreAction().second)
            }
        }
    }

    private fun shouldShowSubscriptionRestoreAction(): Pair<Boolean, String?> {
        return try {
            // Safe call operator used to handle null subscriptions
            billingViewModel.subscriptions.value?.let { subscriptionsList ->
                // Find first restorable subscription
                val restorableSubscription = subscriptionsList.firstOrNull { subscription ->
                    isSubscriptionRestore(subscription)
                }

                // Return appropriate pair based on found subscription
                if (restorableSubscription != null) {
                    Pair(true, restorableSubscription.sku)
                } else {
                    Pair(false, null)
                }
            } ?: Pair(false, null) // Return default if subscriptions list is null
        } catch (e: Exception) {
            // Log error and return safe default
            Timber.e("SubscriptionActivity", "Error checking subscription restore status", e)
            Pair(false, null)
        }
    }

    private fun showChooserDialog() {
        val optionsList: MutableList<DateUpBottomSheet.Item> = arrayListOf()
        billingClientLifecycle.productsWithProductDetails.observe(this, { productList ->
            for ((key, value) in productList) {
                // Get the first offer's pricing
                val price = value.subscriptionOfferDetails?.firstOrNull()?.pricingPhases
                    ?.pricingPhaseList?.firstOrNull()?.formattedPrice ?: continue

                when (key) {
                    SubscriptionConstants.MONTHLY_SKU -> {
                        optionsList.add(
                            DateUpBottomSheet.Item(
                                getString(
                                    R.string.subscription_monthly_title,
                                    price
                                )
                            ) {
                                billingViewModel.buySubscription(SubscriptionConstants.MONTHLY_SKU)
                            })
                    }
                    SubscriptionConstants.THREE_MONTHS_SKU -> {
                        optionsList.add(
                            DateUpBottomSheet.Item(
                                getString(
                                    R.string.subscription_three_months_title,
                                    "$price (save 25%)"
                                )
                            ) {
                                billingViewModel.buySubscription(SubscriptionConstants.THREE_MONTHS_SKU)
                            })
                    }
                    SubscriptionConstants.HALF_YEARLY_SKU -> {
                        optionsList.add(
                            DateUpBottomSheet.Item(
                                getString(
                                    R.string.subscription_half_yearly_title,
                                    "$price (save 50%)"
                                )
                            ) {
                                billingViewModel.buySubscription(SubscriptionConstants.HALF_YEARLY_SKU)
                            })
                    }
                }
            }

            val sortedList = optionsList.sortedWith(compareBy { it.title })

            if (shouldShowSubscriptionRestoreAction().first) {
                DateUpBottomSheet(
                    this,
                    sortedList,
                    getString(R.string.upgrade_to_dateup_plus),
                    getString(R.string.subscription_restore_title)
                ) {
                    manageSubscriptions(shouldShowSubscriptionRestoreAction().second)
                }.show()
            } else {
                DateUpBottomSheet(
                    this,
                    sortedList,
                    getString(R.string.upgrade_to_dateup_plus)
                ).show()
            }
        })
    }

    private fun manageSubscriptions(sku: String?) {
        // Open the Play Store when this event is triggered.
        val url = if (sku == null) {
            // If the SKU is not specified, just open the Google Play subscriptions URL.
            SubscriptionConstants.PLAY_STORE_SUBSCRIPTION_URL
        } else {
            // If the SKU is specified, open the deeplink for this SKU on Google Play.
            String.format(SubscriptionConstants.PLAY_STORE_SUBSCRIPTION_DEEPLINK_URL, sku, packageName)
        }
        val intent = Intent(Intent.ACTION_VIEW)
        intent.data = Uri.parse(url)
        startActivity(intent)
    }

    private fun showSelectChooserDialog() {
        val optionsList: MutableList<DateUpBottomSheet.Item> = arrayListOf()

        billingClientLifecycle.productsWithProductDetails.observe(this) { productMap ->
            val hasExistingPlusSubscription = billingViewModel.subscriptions.value?.any {
                it.sku in listOf(
                    SubscriptionConstants.MONTHLY_SKU,
                    SubscriptionConstants.THREE_MONTHS_SKU,
                    SubscriptionConstants.HALF_YEARLY_SKU,
                    SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER,
                    SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER
                )
            } ?: false

            // Handle SELECT product with multiple offers
            val selectProductDetails = productMap[SubscriptionConstants.SELECT_PRODUCT_ID]
            selectProductDetails?.let { details ->
                details.subscriptionOfferDetails?.forEach { offer ->
                    val pricingPhase = offer.pricingPhases?.pricingPhaseList?.firstOrNull()
                    val formattedPrice = pricingPhase?.formattedPrice ?: ""
                    val offerId = offer.offerId

                    when (offerId) {
                        SubscriptionConstants.SELECT_MONTHLY_OFFER_ID -> {
                            optionsList.add(
                                DateUpBottomSheet.Item(
                                    getString(
                                        R.string.subscription_monthly_title,
                                        if (hasExistingPlusSubscription) "$formattedPrice (Upgrade)" else formattedPrice
                                    )
                                ) {
                                    billingViewModel.buySelectSubscription(SubscriptionConstants.SELECT_PRODUCT_ID, offerId)
                                }
                            )
                        }
                        SubscriptionConstants.SELECT_THREE_MONTHS_OFFER_ID -> {
                            optionsList.add(
                                DateUpBottomSheet.Item(
                                    getString(
                                        R.string.subscription_three_months_title,
                                        if (hasExistingPlusSubscription) "$formattedPrice (Upgrade, save 25%)" else "$formattedPrice (save 25%)"
                                    )
                                ) {
                                    billingViewModel.buySelectSubscription(SubscriptionConstants.SELECT_PRODUCT_ID, offerId)
                                }
                            )
                        }
                        SubscriptionConstants.SELECT_SIX_MONTHS_OFFER_ID -> {
                            optionsList.add(
                                DateUpBottomSheet.Item(
                                    getString(
                                        R.string.subscription_half_yearly_title,
                                        if (hasExistingPlusSubscription) "$formattedPrice (Upgrade, save 50%)" else "$formattedPrice (save 50%)"
                                    )
                                ) {
                                    billingViewModel.buySelectSubscription(SubscriptionConstants.SELECT_PRODUCT_ID, offerId)
                                }
                            )
                        }
                    }
                }
            }

            val sortedList = optionsList.sortedWith(compareBy { it.title })

            if (shouldShowSubscriptionRestoreAction().first) {
                DateUpBottomSheet(
                    this,
                    sortedList,
                    getString(R.string.subscription_upgrade_title_select),
                    getString(R.string.subscription_restore_title)
                ) {
                    manageSubscriptions(shouldShowSubscriptionRestoreAction().second)
                }.show()
            } else {
                DateUpBottomSheet(
                    this,
                    sortedList,
                    getString(R.string.subscription_upgrade_title_select)
                ).show()
            }
        }
    }

    private fun showOfferTrail(counter: Int) {
        binding.trailOfferScrollLayout.visibility = View.VISIBLE

        val promoCounter = counter + 1
        AccountPreferences.getInstance(this@SubscriptionActivity).setValue(com.dateup.android.utils.Constants.promoCounter, promoCounter)

        if (promoCounter == 2) {
            val spannableString: Spannable = SpannableString(getString(R.string.offer_last_chance))
            //spannableString.setSpan(ForegroundColorSpan(getColor(R.color.color_primary)), 38, 49, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.subscriptionTitleHeader.text = spannableString
            binding.subscriptionTitleHeader.visibility = View.VISIBLE
        } else {
            val spannableString: Spannable = SpannableString(getString(R.string.subscription_title_free_trail))
            spannableString.setSpan(
                ForegroundColorSpan(getColor(R.color.color_primary)),
                3,
                16,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            binding.subscriptionTitleHeader.visibility = View.VISIBLE
            binding.subscriptionTitleHeader.text = spannableString
        }
        binding.buttonStartTrail.text = getString(R.string.offer_pricing_title)

        binding.buttonStartTrail.text = getString(R.string.offer_action_button_title)

        binding.buttonStartTrail.setOnClickListener {
            billingViewModel.buySubscription(SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER)
        }
        binding.buttonJoinDateupPlus.visibility = View.GONE
        binding.buttonJoinDateupSelect.visibility = View.GONE

        if (shouldShowSubscriptionRestoreAction().first) {
            binding.restoreTextViewOfferTrail.visibility = View.VISIBLE
            binding.restoreTextViewOfferTrail.setOnClickListener {
                manageSubscriptions(shouldShowSubscriptionRestoreAction().second)
            }
        }
    }

    private fun hideTrailOffer() {
        binding.trailOfferScrollLayout.visibility = View.GONE
        binding.subscriptionSubTitleText.visibility = View.GONE

        binding.buttonJoinDateupPlus.visibility = View.VISIBLE
        binding.buttonJoinDateupSelect.visibility = View.VISIBLE

        val spannableString: Spannable = SpannableString(getString(R.string.upgrade_to_dateup_plus))
        spannableString.setSpan(ForegroundColorSpan(getColor(R.color.color_primary)), 11, 22, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        binding.subscriptionTitleHeader.text = spannableString
    }

    private fun showInfluencerOfferTrail(
        influencerName: String,
        counter: Int
    ) {
        val influencerCounter = counter + 1
        AccountPreferences.getInstance(this@SubscriptionActivity).setValue(com.dateup.android.utils.Constants.promoCounter, influencerCounter)
        binding.trailOfferScrollLayout.visibility = View.VISIBLE

        if (influencerCounter == 2) {
            val spannableString: Spannable = SpannableString(getString(R.string.influencer_offer_last_chance))
            //spannableString.setSpan(ForegroundColorSpan(getColor(R.color.color_primary)), 38, 49, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.subscriptionTitleHeader.text = spannableString
        }else {
            val spannableString: Spannable = SpannableString(getString(R.string.subscription_title_influencer_trail))
            spannableString.setSpan(ForegroundColorSpan(getColor(R.color.color_primary)), 3, 16, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            binding.subscriptionTitleHeader.text = spannableString
        }

        binding.offerTrailPrice.text = getString(R.string.influencer_offer_pricing_title)
        if(influencerName.isNotEmpty()) {
            binding.subscriptionSubTitleText.visibility = View.VISIBLE
            binding.subscriptionSubTitleText.text = getString(R.string.influencer_offer_title, influencerName)
        }else {
            binding.subscriptionSubTitleText.visibility = View.GONE
        }
        binding.buttonStartTrail.text = getString(R.string.influencer_offer_action_button_title)
        binding.buttonStartTrail.setOnClickListener {
            billingViewModel.buySubscription(SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER)
        }
        binding.buttonJoinDateupPlus.visibility = View.GONE
        binding.buttonJoinDateupSelect.visibility = View.GONE

        if (shouldShowSubscriptionRestoreAction().first) {
            binding.restoreTextViewOfferTrail.visibility = View.VISIBLE
            binding.restoreTextViewOfferTrail.setOnClickListener {
                manageSubscriptions(shouldShowSubscriptionRestoreAction().second)
            }
        }
    }

    companion object {
        fun newIntent(context: Context): Intent {
            return Intent(context, SubscriptionActivity::class.java)
        }
        const val FREE_TRAIL = "FREE_TRAIL"
        const val INFLUENCER_FREE_TRAIL = "INFLUENCER_FREE_TRAIL"
        const val SUBSCRIPTION_ORIGIN = "SUBSCRIPTION_ORIGIN"
        const val OUT_OF_LIKES = "OUT_OF_LIKES"
    }
}
