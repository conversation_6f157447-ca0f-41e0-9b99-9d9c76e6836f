package com.dateup.android.subscriptions.billing

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.OnLifecycleEvent
import com.android.billingclient.api.*
import com.android.billingclient.api.Purchase.PurchaseState.PURCHASED
import androidx.lifecycle.LiveData
import com.dateup.android.models.UserObject
import com.dateup.android.subscriptions.SubscriptionConstants
import com.dateup.android.subscriptions.model.PurchaseAcknowledgmentStatus
import com.dateup.android.subscriptions.views.SingleLiveEvent
import timber.log.Timber

@SuppressLint("BinaryOperationInTimber")
class BillingClientLifecycle private constructor(private val app: Application) :
    LifecycleObserver, PurchasesUpdatedListener, BillingClientStateListener,
        SkuDetailsResponseListener, PurchasesResponseListener, ProductDetailsResponseListener {

    /**
     * The purchase event is observable. Only one oberver will be notified.
     */
    val purchaseUpdateEvent = SingleLiveEvent<List<Purchase>>()

    /**
     * Purchases are observable. This list will be updated when the Billing Library
     * detects new or existing purchases. All observers will be notified.
     */
    val purchases = MutableLiveData<List<Purchase>>()

    /**
     * SkuDetails for all known SKUs.
     * @deprecated Use productsWithProductDetails instead
     */
    val skusWithSkuDetails = MutableLiveData<Map<String, SkuDetails>>()
    
    /**
     * ProductDetails for all known products.
     */
    private val _productsWithProductDetails = MutableLiveData<Map<String, ProductDetails>>()
    val productsWithProductDetails: LiveData<Map<String, ProductDetails>> = _productsWithProductDetails

    /**
     * Instantiate a new BillingClient instance.
     */
    lateinit var billingClient: BillingClient

    companion object {
        private const val TAG = "BillingLifecycle"

        private val LIST_OF_SKUS = listOf(
                SubscriptionConstants.MONTHLY_SKU,
                SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER,
                SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER,
                SubscriptionConstants.THREE_MONTHS_SKU,
                SubscriptionConstants.HALF_YEARLY_SKU,
                SubscriptionConstants.SELECT_MONTHLY_SKU,
                SubscriptionConstants.SELECT_THREE_MONTHS_SKU,
                SubscriptionConstants.SELECT_HALF_YEARLY_SKU
                )

        @Volatile
        private var INSTANCE: BillingClientLifecycle? = null

        fun getInstance(app: Application): BillingClientLifecycle =
                INSTANCE ?: synchronized(this) {
                    INSTANCE ?: BillingClientLifecycle(app).also { INSTANCE = it }
                }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
    fun create() {
        Timber.tag(TAG).d("ON_CREATE")
        // Create a new BillingClient in onCreate().
        // Since the BillingClient can only be used once, we need to create a new instance
        // after ending the previous connection to the Google Play Store in onDestroy().
        billingClient = BillingClient.newBuilder(app.applicationContext)
                .setListener(this)
                .enablePendingPurchases() // Not used for subscriptions.
                .build()
        if (!billingClient.isReady) {
            Timber.tag(TAG).d("BillingClient: Start connection...")
            billingClient.startConnection(this)
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun destroy() {
        Timber.tag(TAG).d("ON_DESTROY")
        if (billingClient.isReady) {
            Timber.tag(TAG).d("BillingClient can only be used once -- closing connection")
            // BillingClient can only be used once.
            // After calling endConnection(), we must create a new BillingClient.
            billingClient.endConnection()
        }
    }

    override fun onBillingSetupFinished(billingResult: BillingResult) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        if (responseCode == BillingClient.BillingResponseCode.OK) {
            // The billing client is ready. You can query purchases here.
            querySkuDetails()
            queryProductDetails()
            queryPurchases()
        }
    }

    override fun onBillingServiceDisconnected() {
        Timber.tag(TAG).d("onBillingServiceDisconnected")
        // TODO: Try connecting again with exponential backoff.
        // billingClient.startConnection(this)
    }

    /**
     * In order to make purchasese, you need the [SkuDetails] for the item or subscription.
     * This is an asynchronous call that will receive a result in [onSkuDetailsResponse].
     */
    private fun querySkuDetails() {
        Timber.tag(TAG).d("querySkuDetails")
        val params = SkuDetailsParams.newBuilder()
                .setType(BillingClient.SkuType.SUBS)
                .setSkusList(LIST_OF_SKUS)
                .build()
        params.let { skuDetailsParams ->
            Timber.tag(TAG).i("querySkuDetailsAsync")
            billingClient.querySkuDetailsAsync(skuDetailsParams, this)
        }
    }

    /**
     * Query Google Play Billing for product details.
     *
     * This uses the new Google Play Billing Library 5.0+ API for querying product details.
     */
    private fun queryProductDetails() {
        Timber.tag(TAG).d("queryProductDetails")
        val productList = ArrayList<QueryProductDetailsParams.Product>()
        
        // Add subscription products
        LIST_OF_SKUS.forEach { sku ->
            productList.add(
                QueryProductDetailsParams.Product.newBuilder()
                    .setProductId(sku)
                    .setProductType(BillingClient.ProductType.SUBS)
                    .build()
            )
        }

        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        billingClient.queryProductDetailsAsync(params, this)
    }

    /**
     * Receives the result from [queryProductDetails].
     *
     * Store the ProductDetails and post them in the [productsWithProductDetails]. This allows other parts
     * of the app to use the [ProductDetails] to show product information and make purchases.
     */
    override fun onProductDetailsResponse(
        billingResult: BillingResult,
        productDetailsList: MutableList<ProductDetails>
    ) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        when (responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                Timber.tag(TAG).i("onProductDetailsResponse: $responseCode $debugMessage")
                val expectedProductDetailsCount = LIST_OF_SKUS.size
                if (productDetailsList.isEmpty()) {
                    _productsWithProductDetails.postValue(emptyMap())
                    Timber.tag(TAG).e("onProductDetailsResponse: " +
                            "Expected ${expectedProductDetailsCount}, " +
                            "Found empty ProductDetails list. " +
                            "Check to see if the products you requested are correctly published " +
                            "in the Google Play Console.")
                } else {
                    _productsWithProductDetails.postValue(HashMap<String, ProductDetails>().apply {
                        for (details in productDetailsList) {
                            put(details.productId, details)
                        }
                    })
                }
            }
            BillingClient.BillingResponseCode.SERVICE_DISCONNECTED,
            BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE,
            BillingClient.BillingResponseCode.BILLING_UNAVAILABLE,
            BillingClient.BillingResponseCode.ITEM_UNAVAILABLE,
            BillingClient.BillingResponseCode.DEVELOPER_ERROR,
            BillingClient.BillingResponseCode.ERROR -> {
                Timber.tag(TAG).e("onProductDetailsResponse: $responseCode $debugMessage")
            }
            BillingClient.BillingResponseCode.USER_CANCELED,
            BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED,
            BillingClient.BillingResponseCode.ITEM_NOT_OWNED -> {
                Timber.tag(TAG).d("onProductDetailsResponse: $responseCode $debugMessage")
            }
            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                Timber.tag(TAG).d("onProductDetailsResponse: $responseCode $debugMessage")
            }
        }
    }

    /**
     * Receives the result from [querySkuDetails].
     *
     * Store the SkuDetails and post them in the [skusWithSkuDetails]. This allows other parts
     * of the app to use the [SkuDetails] to show SKU information and make purchases.
     * Request codes: https://developer.android.com/reference/com/android/billingclient/api/BillingClient.BillingResponseCode
     * @deprecated Use [onProductDetailsResponse] instead
     */
    override fun onSkuDetailsResponse(
            billingResult: BillingResult,
            skuDetailsList: MutableList<SkuDetails>?) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        when (responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                Timber.tag(TAG).i("onSkuDetailsResponse: $responseCode $debugMessage")
                val expectedSkuDetailsCount = LIST_OF_SKUS.size
                if (skuDetailsList == null) {
                    skusWithSkuDetails.postValue(emptyMap())
                    Timber.tag(TAG).e("onSkuDetailsResponse: " +
                            "Expected ${expectedSkuDetailsCount}, " +
                            "Found null SkuDetails. " +
                            "Check to see if the SKUs you requested are correctly published " +
                            "in the Google Play Console.")
                } else
                    skusWithSkuDetails.postValue(HashMap<String, SkuDetails>().apply {
                        for (details in skuDetailsList) {
                            put(details.sku, details)
                        }
                    })
            }
            BillingClient.BillingResponseCode.SERVICE_DISCONNECTED,
            BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE,
            BillingClient.BillingResponseCode.BILLING_UNAVAILABLE,
            BillingClient.BillingResponseCode.ITEM_UNAVAILABLE,
            BillingClient.BillingResponseCode.DEVELOPER_ERROR,
            BillingClient.BillingResponseCode.ERROR -> {
                Timber.tag(TAG).e("onSkuDetailsResponse: $responseCode $debugMessage")
            }
            BillingClient.BillingResponseCode.USER_CANCELED,
            BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED,
            BillingClient.BillingResponseCode.ITEM_NOT_OWNED -> {
                Timber.tag(TAG).d( "onSkuDetailsResponse: $responseCode $debugMessage")
            }
            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                Timber.tag(TAG).d( "onSkuDetailsResponse: $responseCode $debugMessage")
            }
        }
    }

    /**
     * Query Google Play Billing for existing purchases.
     *
     * New purchases will be provided to the PurchasesUpdatedListener.
     * You still need to check the Google Play Billing API to know when purchase tokens are removed.
     */
    private fun queryPurchases() {
        if (!billingClient.isReady) {
            Timber.tag(TAG).e("queryPurchases: BillingClient is not ready")
        }
        Timber.tag(TAG).d("queryPurchases: SUBS")

        billingClient.queryPurchasesAsync(BillingClient.SkuType.SUBS, this)
    }

    /**
     * Callback from the billing library when queryPurchasesAsync is called.
     */
    override fun onQueryPurchasesResponse(billingResult: BillingResult,
                                          purchasesList: MutableList<Purchase>) {
        processPurchases(purchasesList)
    }

    /**
     * Launching the billing flow.
     *
     * Launching the UI to make a purchase requires a reference to the Activity.
     */
    fun launchBillingFlow(activity: Activity, params: BillingFlowParams): Int {
        if (!billingClient.isReady) {
            Timber.tag(TAG).e("launchBillingFlow: BillingClient is not ready")
        }
        val billingResult = billingClient.launchBillingFlow(activity, params)
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        Timber.tag(TAG).d("launchBillingFlow: BillingResponse $responseCode $debugMessage")
        return responseCode
    }

    /**
     * Called by the Billing Library when new purchases are detected.
     */
    override fun onPurchasesUpdated(
            billingResult: BillingResult,
            purchases: MutableList<Purchase>?) {
        val responseCode = billingResult.responseCode
        val debugMessage = billingResult.debugMessage
        Timber.tag(TAG).d("onPurchasesUpdated: $responseCode $debugMessage")
        when (responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                if (purchases == null) {
                    Timber.tag(TAG).d("onPurchasesUpdated: null purchase list")
                    processPurchases(null)
                } else {
                    processPurchases(purchases)
                    purchaseUpdateEvent.postValue(purchases)
                }
            }
            BillingClient.BillingResponseCode.USER_CANCELED -> {
                Timber.tag(TAG).i("onPurchasesUpdated: User canceled the purchase")
            }
            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> {
                Timber.tag(TAG).i("onPurchasesUpdated: The user already owns this item")
            }
            BillingClient.BillingResponseCode.DEVELOPER_ERROR -> {
                Timber.tag(TAG).e("onPurchasesUpdated: Developer error means that Google Play " +
                        "does not recognize the configuration. If you are just getting started, " +
                        "make sure you have configured the application correctly in the " +
                        "Google Play Console. The SKU product ID must match and the APK you " +
                        "are using must be signed with release keys."
                )
            }
        }
    }

    /**
     * Send purchase SingleLiveEvent and update purchases LiveData.
     *
     * The SingleLiveEvent will trigger network call to verify the subscriptions on the sever.
     * The LiveData will allow Google Play settings UI to update based on the latest purchase data.
     */
    private fun processPurchases(purchasesList: List<Purchase>?) {
        Timber.tag(TAG).d("processPurchases: ${purchasesList?.size} purchase(s)")
        
        if (!purchasesList.isNullOrEmpty()) {
            val hasActivePlusSubscription = purchasesList.any { purchase ->
                purchase.skus.any { sku ->
                    sku in listOf(
                        SubscriptionConstants.MONTHLY_SKU,
                        SubscriptionConstants.THREE_MONTHS_SKU,
                        SubscriptionConstants.HALF_YEARLY_SKU,
                        SubscriptionConstants.MONTHLY_SKU_WITH_TRAIL_OFFER,
                        SubscriptionConstants.INFLUENCER_MONTHLY_SKU_WITH_TRAIL_OFFER
                    )
                }
            }

            val hasActiveSelectSubscription = purchasesList.any { purchase ->
                purchase.skus.any { sku ->
                    sku in listOf(
                        SubscriptionConstants.SELECT_MONTHLY_SKU,
                        SubscriptionConstants.SELECT_THREE_MONTHS_SKU,
                        SubscriptionConstants.SELECT_HALF_YEARLY_SKU,
                    )
                }
            }

            // Update subscription status in UserObject
            UserObject.isDateUpPlusUser = hasActivePlusSubscription
            UserObject.isDateUpSelectUser = hasActiveSelectSubscription
        } else {
            UserObject.isDateUpPlusUser = false
            UserObject.isDateUpSelectUser = false
            // There is a possibility that the user purchased dateup plus/select from iOS
            // and switched to android
            // check the database to determine if the user is dateup plus/select user.
            // Should get the dateupPlus/userId node and see if the user status is active and not expired
        }
        
        purchases.postValue(purchasesList)
    }

    /**
     * Check whether the purchases have changed before posting changes.
     */
    private fun isUnchangedPurchaseList(purchasesList: List<Purchase>?): Boolean {
        // TODO: Optimize to avoid updates with identical data.
        return false
    }

    /**
     * Log the number of purchases that are acknowledge and not acknowledged.
     *
     * https://developer.android.com/google/play/billing/billing_library_releases_notes#2_0_acknowledge
     *
     * When the purchase is first received, it will not be acknowledge.
     * This application sends the purchase token to the server for registration. After the
     * purchase token is registered to an account, the Android app acknowledges the purchase token.
     * The next time the purchase list is updated, it will contain acknowledged purchases.
     */
    private fun logAcknowledgementStatus(purchasesList: List<Purchase>) {
        var ack_yes = 0
        var ack_no = 0
        for (purchase in purchasesList) {
            if (purchase.isAcknowledged) {
                ack_yes++
            } else {
                ack_no++
            }
        }
        Timber.tag(TAG).d("logAcknowledgementStatus: acknowledged=$ack_yes unacknowledged=$ack_no")
    }

    /**
     * Acknowledge a purchase.
     *
     * https://developer.android.com/google/play/billing/billing_library_releases_notes#2_0_acknowledge
     *
     * Apps should acknowledge the purchase after confirming that the purchase token
     * has been associated with a user. This app only acknowledges purchases after
     * successfully receiving the subscription data back from the server.
     *
     * Developers can choose to acknowledge purchases from a server using the
     * Google Play Developer API. The server has direct access to the user database,
     * so using the Google Play Developer API for acknowledgement might be more reliable.
     * TODO(134506821): Acknowledge purchases on the server.
     *
     * If the purchase token is not acknowledged within 3 days,
     * then Google Play will automatically refund and revoke the purchase.
     * This behavior helps ensure that users are not charged for subscriptions unless the
     * user has successfully received access to the content.
     * This eliminates a category of issues where users complain to developers
     * that they paid for something that the app is not giving to them.
     */
    fun acknowledgePurchases(purchasesList: List<Purchase>, callback: (List<PurchaseAcknowledgmentStatus>) -> Unit) {
        Timber.tag(TAG).d("acknowledgePurchase")
        val purchaseAcknowledgmentStatusList = arrayListOf<PurchaseAcknowledgmentStatus>()
        for (purchase in purchasesList) {
            if (purchase.purchaseState == PURCHASED && !purchase.isAcknowledged) {
                val params = AcknowledgePurchaseParams.newBuilder()
                        .setPurchaseToken(purchase.purchaseToken)
                        .build()
                billingClient.acknowledgePurchase(params) { billingResult ->
                    val responseCode = billingResult.responseCode
                    val debugMessage = billingResult.debugMessage
                    Timber.tag(TAG).d("acknowledgePurchase for token: $responseCode $debugMessage")
                    if (responseCode != 1 && responseCode != 2 && responseCode != -3 && responseCode != -1 && responseCode != 6) {
                        purchaseAcknowledgmentStatusList.add(PurchaseAcknowledgmentStatus(purchase.purchaseToken, true))
                    }
                }
            }
        }
        callback(purchaseAcknowledgmentStatusList)
    }
}

