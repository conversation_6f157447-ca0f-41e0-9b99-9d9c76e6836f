package com.dateup.android.analytics

import android.content.Context
import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics

// https://firebase.google.com/docs/reference/android/com/google/firebase/analytics/FirebaseAnalytics.Event
// Custom events
const val APP_SHARE = "app_share"
const val MUTUAL_MATCH = "mutual_match"
const val ONBOARDING_COMPLETE = "onboarding_complete"
const val ICE_BREAKERS_COMPLETE = "ice_breakers_complete"
const val LIKE_USER = "like_user"
const val DISLIKE_USER = "dislike_user"

const val SIGN_IN_GOOGLE = "google"
const val SIGN_IN_FACEBOOK = "facebook"

const val ACCOUNT_DELETE = "account_delete"
const val HEIGHT_VERIFICATION = "height_verification"
const val OTP_VERIFICATION = "otp_verification"
const val OTP_DIDNT_RECEIVE_CODE = "otp_didnt_receive_code"
const val SIGN_IN_THROUGH_OTP = "sign_in_through_otp"

// App Review
const val APP_REVIEW_LOVE_IT = "app_review_love_it"
const val APP_REVIEW_NEEDS_WORK = "app_review_needs_work"
const val APP_REVIEW_CLOSED = "app_review_closed"
const val APP_REVIEW_WRITE = "app_review_write"

// User Properties
const val USER_AGE_PROPERTY = "user_age"
const val USER_GENDER_PROPERTY = "user_gender"
const val USER_ORIENTATION_PROPERTY = "user_orientation"
const val USER_IS_A_MEMBER_PROPERTY = "is_a_member"

// User property values
const val STRAIGHT = "straight"
const val GAY = "gay"
const val ANALYTICS_NO = "no"
const val ANALYTICS_YES = "yes"

// Params possible values
const val ANALYTICS_SUCCESS = "success"
const val ANALYTICS_FAILURE = "failure"
const val ANALYTICS_UNKNOWN = "Unknown"

// Params
const val STATUS = "status"
const val ERROR_REASON = "error_reason"
const val ERROR_CODE = "error_code"


class AnalyticsTrackingService {

    companion object {
        fun setUserId(context: Context?, userId: String?) {

            context?.let { ctx ->
                FirebaseAnalytics.getInstance(ctx)
                        .setUserId(userId)
            }
        }

        fun logEvent(context: Context?, name: String,
                     bundle: Bundle? = null) {

            context?.let { ctx ->
                FirebaseAnalytics.getInstance(ctx)
                        .logEvent(name, bundle)
            }
        }

        fun setUserProperty(context: Context?, name: String,
                            value: String?) {

            context?.let { ctx ->
                FirebaseAnalytics.getInstance(ctx)
                        .setUserProperty(name, value)
            }
        }

        fun resetUserProperties(context: Context?) {

            setUserId(context, null)
            setUserProperty(context, USER_AGE_PROPERTY, null)
            setUserProperty(context, USER_GENDER_PROPERTY, null)
            setUserProperty(context, USER_ORIENTATION_PROPERTY, null)
            setUserProperty(context, USER_IS_A_MEMBER_PROPERTY, null)
        }
    }
}
