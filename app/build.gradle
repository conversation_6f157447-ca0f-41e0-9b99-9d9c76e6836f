apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'kotlin-parcelize'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'

android {

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    defaultConfig {
        compileSdk 35
        minSdkVersion 26
        targetSdkVersion 35
        versionCode 157
        versionName generateVersionName()
        resConfigs "en"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            storeFile file('dateup-release')
            storePassword "ThaiCurryWings2018"
            keyAlias "DateUpRelease1"
            keyPassword "ThaiCurryWingsExtraWet2018**"
        }
    }

    buildTypes {

        release {
            minifyEnabled false
            shrinkResources false
            debuggable true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // Generate native symbols that gets uploaded to playstore
            ndk {
                debugSymbolLevel = 'FULL'
            }

            ndkVersion "22.0.7026061"
        }
        debug {
            versionNameSuffix "-SNAPSHOT"
            debuggable true
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // Generate native symbols that gets uploaded to playstore
            ndk {
                debugSymbolLevel = 'FULL'
            }

            ndkVersion "22.0.7026061"
        }
        perf {
            // initWith debug
            versionNameSuffix "-Perf"
            minifyEnabled false
            shrinkResources false
            debuggable true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    buildFeatures {
        viewBinding true
    }

    lintOptions {
        checkReleaseBuilds true

        testOptions {
            // Used for Unit testing Android dependent elements in /test folder
            unitTests.includeAndroidResources = true
            unitTests.returnDefaultValues = true
        }
        lint {
            abortOnError false
            checkReleaseBuilds true
        }
        namespace 'com.dateup.android'
    }
}

private static String generateVersionName() {
    Integer versionMajor = 8
    Integer versionMinor = 0
    Integer versionPatch = 0
    Integer buildNumber = 0
    return versionMajor + "." + versionMinor + "." + versionPatch + "(" + buildNumber + ")"
}

dependencies {
    // Jar libs
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Support libs
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // ViewModel and LiveData extensions.
    implementation "android.arch.lifecycle:extensions:1.1.1"

    // Java8 support for Lifecycles.
    implementation "android.arch.lifecycle:common-java8:1.1.1"

    // Room Architecture Components.
    implementation "android.arch.persistence.room:runtime:1.1.1"
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    testImplementation 'junit:junit:4.13.2'
    testImplementation "android.arch.core:core-testing:1.1.1"

    androidTestImplementation 'junit:junit:4.13.2'
    androidTestImplementation "android.arch.core:core-testing:1.1.1"
    // Optional -- UI testing with Espresso
    implementation "com.android.support.test.espresso:espresso-idling-resource:3.0.2"
    // Optional -- UI testing with UI Automator

    // Kotlin annotation processor for Room.
    kapt "android.arch.persistence.room:compiler:1.1.1"

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7'

    implementation 'androidx.room:room-runtime:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    implementation 'androidx.room:room-rxjava2:2.6.1'
    implementation 'androidx.cardview:cardview:1.0.0'

    implementation 'com.tbuonomo:dotsindicator:4.3'



    //workmanager using for glide
    implementation 'androidx.work:work-runtime-ktx:2.10.0'

    //Glide
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    kapt 'com.github.bumptech.glide:compiler:4.16.0'

    //Lottie
    implementation 'com.airbnb.android:lottie:6.3.0'

    //segmented control
    implementation 'com.github.ceryle:SegmentedButton:v2.0.2'

    //multislider
    implementation 'io.apptik.widget:multislider:1.3'

    //location
    implementation 'com.google.android.gms:play-services-location:21.3.0'

    //geofire
    implementation 'com.firebase:geofire-android:3.2.0'

    //chat kit
    implementation('com.github.stfalcon-studio:Chatkit:0.4.1') {
        exclude group: 'com.google.android', module: 'flexbox'
    }

    // Add the new flexbox library to replace the excluded one
    implementation 'com.google.android.flexbox:flexbox:3.0.0'
    //Timber
    implementation 'com.jakewharton.timber:timber:5.0.1'

    //https://github.com/DanielMartinus/Konfetti
    implementation 'nl.dionsegijn:konfetti:1.3.2'

    //google play billing
    implementation "com.android.billingclient:billing-ktx:7.1.1"

    implementation 'androidx.browser:browser:1.8.0'

    implementation 'com.google.code.gson:gson:2.10.1'

    // For photo orientation
    implementation 'androidx.exifinterface:exifinterface:1.3.7'

    // Import the BoM for the Firebase platform
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation platform('com.google.firebase:firebase-bom:33.6.0')

    // Analytics
    implementation 'com.google.firebase:firebase-analytics-ktx'
    // Firebase functions
    implementation 'com.google.firebase:firebase-functions-ktx'
    // dynamic links
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
    // Performance
    implementation 'com.google.firebase:firebase-perf-ktx'
    // In-App messaging
    implementation 'com.google.firebase:firebase-inappmessaging-display-ktx'
    //Firebase
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-database-ktx'
    implementation 'com.google.firebase:firebase-messaging:24.1.0'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'

    // Firebase ML Kit
    implementation 'com.google.mlkit:barcode-scanning:17.3.0'
    implementation 'com.google.mlkit:object-detection-custom:17.0.1'

    // AWS S3 SDK
    implementation 'com.amazonaws:aws-android-sdk-s3:2.75.0'

    // https://github.com/Shouheng88/Compressor
    // Image compression library
    implementation 'com.github.Shouheng88:compressor:1.6.0'


    // https://github.com/jkwiecien/EasyImage
    implementation 'com.github.jkwiecien:EasyImage:3.2.0'

    // library used for app update
    // This dependency is downloaded from the Google’s Maven repository.
    // Make sure you also include that repository in your project's build.gradle file.
    implementation 'com.google.android.play:app-update:2.1.0'
    // Play Services
    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'
    1

    // For Kotlin users, also add the Kotlin extensions library for Play In-App Update:
    implementation 'com.google.android.play:app-update-ktx:2.1.0'

    // app check
    implementation 'com.google.firebase:firebase-appcheck-safetynet:16.1.2'
    implementation 'com.google.firebase:firebase-appcheck-debug:18.0.0'
    implementation("com.google.firebase:firebase-appcheck-playintegrity")

    // places
    implementation 'com.google.android.libraries.places:places:4.1.0'

    // ucrop https://github.com/Yalantis/uCrop
    implementation 'com.github.yalantis:ucrop:2.2.10'

    //Branch.io
    implementation 'io.branch.sdk.android:library:5.8.2'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'

    // leakcanary
    // debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.7'

    // facebook
    implementation 'com.facebook.android:facebook-login:17.0.0'

    //Junit
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.1'
    //For runBlockingTest, CoroutineDispatcher etc.
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.8.0'
}

apply plugin: 'com.google.gms.google-services'
